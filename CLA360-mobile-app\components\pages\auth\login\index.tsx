import InputField from "@/components/InputField";
import CustomButton from "@/components/customButton";
import { icons, images } from "@/constants";
import { Link } from "expo-router";
import { useState } from "react";
import { Image, ScrollView, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useSignIn } from '@clerk/clerk-expo'
import { useRouter } from 'expo-router'
import React from "react";

const SignInComponent = () => {
    const { signIn, setActive, isLoaded } = useSignIn()
    const router = useRouter()

    const [form, setForm] = useState({
        email: "",
        password: "",
    }
    );

    const onSignInPress = React.useCallback(async () => {
        if (!isLoaded) {
            return
        }

        try {
            const signInAttempt = await signIn.create({
                identifier: form.email,
                password: form.password,
            })

            if (signInAttempt.status === 'complete') {
                await setActive({ session: signInAttempt.createdSessionId })
                router.replace('/')
            } else {
                // See https://clerk.com/docs/custom-flows/error-handling
                // for more info on error handling
                console.error(JSON.stringify(signInAttempt, null, 2))
            }
        } catch (err: any) {
            console.error(JSON.stringify(err, null, 2))
        }
    }, [isLoaded, form.email, form.password])

    return (
        <ScrollView className="flex-1 bg-white">
            <View className="flex-1 bg-white">
                <View className="flex w-full h-[250px] items-center justify-center">
                    <Image
                        source={images.logo}
                        className="z-0 w-full h-[250px]"
                        resizeMode="contain"
                    />
                    <Text className="text-2xl text-black font-JakartaSemiBold">Welcome Back</Text>
                </View>
                <View className="p-5">
                    <InputField label="Email" placeholder="Email" icon={icons.email} value={form.email} onChangeText={(value) => setForm({ ...form, email: value })} />
                    <InputField label="Password" placeholder="Password" icon={icons.lock} secureTextEntry={true} value={form.password} onChangeText={(value) => setForm({ ...form, password: value })} />
                    <CustomButton title="Sign In" onPress={onSignInPress} className="mt-6" />

                    <Link href="/sign-up" className="text-lg text-center text-general-200 mt-10">
                        <Text>Don't Have an Account?</Text>
                        <Text className="text-primary-500">Sign Up</Text>
                    </Link>
                </View>
            </View>
        </ScrollView>
    )
}

export default SignInComponent;
